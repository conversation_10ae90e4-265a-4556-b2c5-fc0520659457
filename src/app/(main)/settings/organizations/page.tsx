'use client';

import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import Link from 'next/link';
import SettingsLayout from '../components/SettingsLayout';
import { Plus, Building2, ArrowRight, Loader2 } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { getCookie } from 'cookies-next';
import { appTheme as settingsTheme, appStyles as commonStyles } from '@/app/theme';
const OrganizationsContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${settingsTheme.spacing.xl};
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: ${settingsTheme.spacing.xl};
  background-color: ${settingsTheme.colors.background.main};
  border-radius: ${settingsTheme.borderRadius.lg};
  box-shadow: ${settingsTheme.shadows.md};

  /* Large desktop */
  @media (min-width: ${settingsTheme.breakpoints['2xl']}) {
    max-width: 1400px;
    padding: ${settingsTheme.spacing['2xl']};
    gap: ${settingsTheme.spacing['2xl']};
  }

  /* Tablet */
  @media (max-width: ${settingsTheme.breakpoints.lg}) and (min-width: ${settingsTheme.breakpoints.md}) {
    max-width: 100%;
    padding: ${settingsTheme.spacing.lg};
    gap: ${settingsTheme.spacing.lg};
    border-radius: ${settingsTheme.borderRadius.md};
  }

  /* Mobile */
  @media (max-width: ${settingsTheme.breakpoints.md}) {
    padding: ${settingsTheme.spacing.md};
    gap: ${settingsTheme.spacing.md};
    border-radius: ${settingsTheme.borderRadius.sm};
    box-shadow: ${settingsTheme.shadows.sm};
  }

  /* Small mobile */
  @media (max-width: ${settingsTheme.breakpoints.sm}) {
    padding: ${settingsTheme.spacing.sm};
    gap: ${settingsTheme.spacing.sm};
    border-radius: 0;
    box-shadow: none;
    background-color: transparent;
  }
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${settingsTheme.spacing.lg};
  flex-wrap: wrap;
  gap: ${settingsTheme.spacing.md};

  /* Mobile */
  @media (max-width: ${settingsTheme.breakpoints.md}) {
    flex-direction: column;
    align-items: stretch;
    gap: ${settingsTheme.spacing.sm};
    margin-bottom: ${settingsTheme.spacing.md};
  }

  /* Small mobile */
  @media (max-width: ${settingsTheme.breakpoints.sm}) {
    margin-bottom: ${settingsTheme.spacing.sm};
  }
`;

const Title = styled.h2`
  ${commonStyles.sectionTitle}
`;

const AddButton = styled.button`
  ${commonStyles.button.primary}
  display: flex;
  align-items: center;
  gap: ${settingsTheme.spacing.sm};

  /* Mobile */
  @media (max-width: ${settingsTheme.breakpoints.md}) {
    width: 100%;
    justify-content: center;
    min-height: 44px;
    padding: ${settingsTheme.spacing.md} ${settingsTheme.spacing.lg};
    font-size: ${settingsTheme.typography.fontSizes.base};
  }

  /* Small mobile */
  @media (max-width: ${settingsTheme.breakpoints.sm}) {
    min-height: 40px;
    padding: ${settingsTheme.spacing.sm} ${settingsTheme.spacing.md};
    font-size: ${settingsTheme.typography.fontSizes.sm};
  }
`;

const OrganizationGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: ${settingsTheme.spacing.lg};

  /* Large desktop */
  @media (min-width: ${settingsTheme.breakpoints['2xl']}) {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: ${settingsTheme.spacing['2xl']};
  }

  /* Desktop */
  @media (min-width: ${settingsTheme.breakpoints.xl}) and (max-width: ${settingsTheme.breakpoints['2xl']}) {
    grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
    gap: ${settingsTheme.spacing.xl};
  }

  /* Tablet */
  @media (max-width: ${settingsTheme.breakpoints.lg}) and (min-width: ${settingsTheme.breakpoints.md}) {
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: ${settingsTheme.spacing.md};
  }

  /* Mobile */
  @media (max-width: ${settingsTheme.breakpoints.md}) {
    grid-template-columns: 1fr;
    gap: ${settingsTheme.spacing.sm};
  }

  /* Small mobile */
  @media (max-width: ${settingsTheme.breakpoints.sm}) {
    gap: ${settingsTheme.spacing.xs};
  }
`;

const OrganizationCard = styled.div`
  ${commonStyles.card}
  border: 1px solid ${settingsTheme.colors.border};
  transition: ${settingsTheme.transitions.default};

  &:hover {
    box-shadow: ${settingsTheme.shadows.md};
    border-color: ${settingsTheme.colors.primary};
    transform: translateY(-2px);
  }
`;

const OrgIcon = styled.div`
  width: 48px;
  height: 48px;
  border-radius: ${settingsTheme.borderRadius.md};
  background-color: ${settingsTheme.colors.primaryLight};
  color: ${settingsTheme.colors.primary};
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: ${settingsTheme.spacing.lg};
`;

const OrgImage = styled.img`
  width: 48px;
  height: 48px;
  border-radius: ${settingsTheme.borderRadius.md};
  object-fit: cover;
  margin-bottom: ${settingsTheme.spacing.lg};
`;

const OrgName = styled.h3`
  font-size: ${settingsTheme.typography.fontSizes.lg};
  font-weight: ${settingsTheme.typography.fontWeights.medium};
  color: ${settingsTheme.colors.text.primary};
  margin-bottom: ${settingsTheme.spacing.sm};
`;

const OrgDescription = styled.p`
  font-size: ${settingsTheme.typography.fontSizes.sm};
  color: ${settingsTheme.colors.text.tertiary};
  margin-bottom: ${settingsTheme.spacing.lg};
`;

const OrgLink = styled(Link)`
  display: flex;
  align-items: center;
  font-size: ${settingsTheme.typography.fontSizes.sm};
  color: ${settingsTheme.colors.primary};
  text-decoration: none;
  gap: ${settingsTheme.spacing.xs};
  font-weight: ${settingsTheme.typography.fontWeights.medium};
  transition: ${settingsTheme.transitions.default};

  &:hover {
    color: ${settingsTheme.colors.primaryHover};
    text-decoration: underline;
  }
`;

const EmptyState = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: ${settingsTheme.spacing['2xl']} ${settingsTheme.spacing.base};
  text-align: center;
  background-color: ${settingsTheme.colors.background.light};
  border-radius: ${settingsTheme.borderRadius.lg};
  border: 1px dashed ${settingsTheme.colors.border};
`;

const EmptyStateTitle = styled.h3`
  font-size: ${settingsTheme.typography.fontSizes.lg};
  font-weight: ${settingsTheme.typography.fontWeights.medium};
  color: ${settingsTheme.colors.text.primary};
  margin-bottom: ${settingsTheme.spacing.sm};
`;

const EmptyStateDescription = styled.p`
  font-size: ${settingsTheme.typography.fontSizes.sm};
  color: ${settingsTheme.colors.text.tertiary};
  margin-bottom: ${settingsTheme.spacing.xl};
`;

const LoadingContainer = styled.div`
  width: 100%;
  min-height: 200px;
`;

const SkeletonCard = styled.div`
  ${commonStyles.card}
  border: 1px solid ${settingsTheme.colors.border};
  height: 180px;
  display: flex;
  flex-direction: column;
`;

const SkeletonIcon = styled.div`
  width: 48px;
  height: 48px;
  border-radius: ${settingsTheme.borderRadius.md};
  background-color: ${settingsTheme.colors.background.light};
  margin-bottom: ${settingsTheme.spacing.lg};
  animation: pulse 1.5s ease-in-out infinite;
`;

const SkeletonTitle = styled.div`
  height: 20px;
  width: 70%;
  background-color: ${settingsTheme.colors.background.light};
  border-radius: ${settingsTheme.borderRadius.sm};
  margin-bottom: ${settingsTheme.spacing.sm};
  animation: pulse 1.5s ease-in-out infinite;
`;

const SkeletonDescription = styled.div`
  height: 16px;
  width: 90%;
  background-color: ${settingsTheme.colors.background.light};
  border-radius: ${settingsTheme.borderRadius.sm};
  margin-bottom: ${settingsTheme.spacing.lg};
  animation: pulse 1.5s ease-in-out infinite;
`;

const SkeletonLink = styled.div`
  height: 16px;
  width: 50%;
  background-color: ${settingsTheme.colors.background.light};
  border-radius: ${settingsTheme.borderRadius.sm};
  animation: pulse 1.5s ease-in-out infinite;
`;

const GlobalStyle = styled.div`
  @keyframes pulse {
    0% {
      opacity: 0.6;
    }
    50% {
      opacity: 0.8;
    }
    100% {
      opacity: 0.6;
    }
  }
`;

const ErrorContainer = styled.div`
  padding: ${settingsTheme.spacing.base};
  background-color: ${settingsTheme.colors.error.light};
  border: 1px solid ${settingsTheme.colors.error.main};
  border-radius: ${settingsTheme.borderRadius.md};
  color: ${settingsTheme.colors.error.main};
  margin-bottom: ${settingsTheme.spacing.lg};
`;

const Modal = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
`;

const ModalContent = styled.div`
  background-color: ${settingsTheme.colors.background.main};
  padding: ${settingsTheme.spacing['2xl']};
  border-radius: ${settingsTheme.borderRadius.lg};
  width: 100%;
  max-width: 500px;
  box-shadow: ${settingsTheme.shadows.lg};

  /* Tablet */
  @media (max-width: ${settingsTheme.breakpoints.lg}) and (min-width: ${settingsTheme.breakpoints.md}) {
    max-width: 450px;
    padding: ${settingsTheme.spacing.xl};
    border-radius: ${settingsTheme.borderRadius.md};
  }

  /* Mobile */
  @media (max-width: ${settingsTheme.breakpoints.md}) {
    max-width: 95vw;
    padding: ${settingsTheme.spacing.lg};
    border-radius: ${settingsTheme.borderRadius.sm};
    margin: ${settingsTheme.spacing.sm};
  }

  /* Small mobile */
  @media (max-width: ${settingsTheme.breakpoints.sm}) {
    max-width: 100vw;
    padding: ${settingsTheme.spacing.md};
    border-radius: 0;
    margin: 0;
    height: 100vh;
    overflow-y: auto;
  }
`;

const ModalHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${settingsTheme.spacing.xl};
`;

const ModalTitle = styled.h3`
  font-size: ${settingsTheme.typography.fontSizes.xl};
  font-weight: ${settingsTheme.typography.fontWeights.semibold};
  color: ${settingsTheme.colors.text.primary};
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  cursor: pointer;
  font-size: ${settingsTheme.typography.fontSizes['2xl']};
  color: ${settingsTheme.colors.text.tertiary};
  transition: ${settingsTheme.transitions.default};

  &:hover {
    color: ${settingsTheme.colors.text.secondary};
  }
`;

const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: ${settingsTheme.spacing.lg};
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${settingsTheme.spacing.sm};
  margin-bottom: ${settingsTheme.spacing.md};
  width: 100%;
`;

const Label = styled.label`
  font-size: ${settingsTheme.typography.fontSizes.sm};
  font-weight: ${settingsTheme.typography.fontWeights.medium};
  color: ${settingsTheme.colors.text.secondary};
  margin-bottom: ${settingsTheme.spacing.xs};
`;

const Input = styled.input`
  ${commonStyles.input}
`;

const TextArea = styled.textarea`
  ${commonStyles.input}
  min-height: 100px;
  resize: vertical;
`;

const ButtonGroup = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 1rem;

  /* Mobile */
  @media (max-width: ${settingsTheme.breakpoints.md}) {
    flex-direction: column-reverse;
    gap: ${settingsTheme.spacing.sm};
  }

  /* Small mobile */
  @media (max-width: ${settingsTheme.breakpoints.sm}) {
    gap: ${settingsTheme.spacing.xs};
  }
`;

const CancelButton = styled.button`
  padding: 0.625rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  background-color: white;
  color: #374151;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;

  &:hover {
    background-color: #f9fafb;
  }
`;

const SubmitButton = styled.button`
  ${commonStyles.button.primary}
`;

// Type definitions for organization data
interface Department {
  id: number;
  name: string;
  description: string | null;
}

interface Organization {
  id: number;
  name: string;
  description: string | null;
  imageUrl: string | null;
  departments: Department[];
}

export default function OrganizationsPage() {
  const router = useRouter();
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [filteredOrganizations, setFilteredOrganizations] = useState<Organization[]>([]);

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showModal, setShowModal] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [userRole, setUserRole] = useState<number | null>(null);

  // Form state for creating a new organization
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    imageUrl: '',
  });

  // Fetch user role and organizations on component mount
  useEffect(() => {
    fetchUserRole();
    fetchOrganizations();
  }, []);

  // Set filtered organizations to all organizations
  useEffect(() => {
    setFilteredOrganizations(organizations);
  }, [organizations]);

  // Fetch user role from API
  const fetchUserRole = async () => {
    try {
      const token = getCookie('access_token');
      if (!token) return;

      const response = await fetch('/api/v1/me', {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setUserRole(data.user?.role?.id || null);
      }
    } catch (error) {
      console.error('Error fetching user role:', error);
    }
  };

  // Fetch organizations from API
  const fetchOrganizations = async () => {
    setLoading(true);
    setError(null);

    try {
      // Get token from localStorage (assuming authentication is set up)
      const token = getCookie('access_token');

      if (!token) {
        setError('Authentication required. Please log in.');
        setLoading(false);
        return;
      }

      const response = await fetch('/api/v1/organization', {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch organizations: ${response.statusText}`);
      }

      const data = await response.json();
      setOrganizations(data.organizations);
      setFilteredOrganizations(data.organizations);
    } catch (err) {
      console.error('Error fetching organizations:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch organizations');
    } finally {
      setLoading(false);
    }
  };

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  // Handle form submission to create a new organization
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitting(true);

    try {
      const token = getCookie('access_token');

      if (!token) {
        setError('Authentication required. Please log in.');
        setSubmitting(false);
        return;
      }

      const response = await fetch('/api/v1/organization', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create organization');
      }

      // Reset form and close modal
      setFormData({ name: '', description: '', imageUrl: '' });
      setShowModal(false);

      // Refresh organizations list
      fetchOrganizations();
    } catch (err) {
      console.error('Error creating organization:', err);
      setError(err instanceof Error ? err.message : 'Failed to create organization');
    } finally {
      setSubmitting(false);
    }
  };

  

  return (
    <SettingsLayout>
      <OrganizationsContainer>
        <Header>
          <Title>Organizations</Title>
          {userRole === 1 && (
            <AddButton onClick={() => setShowModal(true)}>
              <Plus size={16} />
              Add Organization
            </AddButton>
          )}
        </Header>

        {error && <ErrorContainer>{error}</ErrorContainer>}

        <GlobalStyle />
        {loading ? (
          <LoadingContainer>
            <OrganizationGrid>
              {[...Array(4)].map((_, index) => (
                <SkeletonCard key={index}>
                  <SkeletonIcon />
                  <SkeletonTitle />
                  <SkeletonDescription />
                  <SkeletonLink />
                </SkeletonCard>
              ))}
            </OrganizationGrid>
          </LoadingContainer>
        ) : filteredOrganizations.length > 0 ? (
          <OrganizationGrid>
            {filteredOrganizations.map(org => (
              <OrganizationCard key={org.id}>
                {org.imageUrl ? (
                  <OrgImage src={org.imageUrl} alt={`${org.name} logo`} />
                ) : (
                  <OrgIcon>
                    <Building2 size={24} />
                  </OrgIcon>
                )}
                <OrgName>{org.name}</OrgName>
                <OrgDescription>{org.description || 'No description'}</OrgDescription>
                <OrgLink href={`/settings/organizations/${org.id}/department`}>
                  Manage
                  <ArrowRight size={14} />
                </OrgLink>
              </OrganizationCard>
            ))}
          </OrganizationGrid>
        ) : (
          <EmptyState>
            <Building2 size={48} color="#9ca3af" />
            <EmptyStateTitle>No organizations found</EmptyStateTitle>
            <EmptyStateDescription>
              {userRole === 1
                ? "You haven't created any organizations yet."
                : "You don't have access to any organizations yet."
              }
            </EmptyStateDescription>
            {userRole === 1 && (
              <AddButton onClick={() => setShowModal(true)}>
                <Plus size={16} />
                Create your first organization
              </AddButton>
            )}
          </EmptyState>
        )}
      </OrganizationsContainer>

      {/* Add Organization Modal */}
      {showModal && (
        <Modal>
          <ModalContent>
            <ModalHeader>
              <ModalTitle>Create New Organization</ModalTitle>
              <CloseButton onClick={() => setShowModal(false)}>&times;</CloseButton>
            </ModalHeader>
            <Form onSubmit={handleSubmit}>
              <FormGroup>
                <Label htmlFor="name">Organization Name *</Label>
                <Input
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  required
                />
              </FormGroup>
              <FormGroup>
                <Label htmlFor="description">Description</Label>
                <TextArea
                  id="description"
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                />
              </FormGroup>
              <FormGroup>
                <Label htmlFor="imageUrl">Logo URL</Label>
                <Input
                  id="imageUrl"
                  name="imageUrl"
                  value={formData.imageUrl}
                  onChange={handleInputChange}
                  placeholder="https://example.com/logo.png"
                />
              </FormGroup>
              <ButtonGroup>
                <CancelButton type="button" onClick={() => setShowModal(false)}>
                  Cancel
                </CancelButton>
                <SubmitButton type="submit" disabled={submitting || !formData.name.trim()}>
                  {submitting ? (
                    <>
                      <Loader2 size={16} className="animate-spin" />
                      Creating...
                    </>
                  ) : (
                    'Create Organization'
                  )}
                </SubmitButton>
              </ButtonGroup>
            </Form>
          </ModalContent>
        </Modal>
      )}
    </SettingsLayout>
  );
}
